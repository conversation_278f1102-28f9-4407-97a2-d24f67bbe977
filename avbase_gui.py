import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import requests
from bs4 import BeautifulSoup
import re
import sqlite3
import threading
import os

class AVBaseScraper:
    def __init__(self):
        self.base_url = "https://avbase.net"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.init_db()
    
    def init_db(self):
        """初始化数据库"""
        conn = sqlite3.connect('avbase.db')
        cursor = conn.cursor()

        # 主影片信息表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS av_info (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT NOT NULL,
                maker TEXT DEFAULT '',
                cid TEXT,
                title TEXT,
                release_date TEXT,
                label TEXT,
                series TEXT,
                director TEXT,
                dmm_url TEXT,
                cover_image TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(code, maker)
            )
        ''')

        # 演员表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS actresses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 影片-演员关联表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS av_actresses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                av_id INTEGER NOT NULL,
                actress_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (av_id) REFERENCES av_info (id) ON DELETE CASCADE,
                FOREIGN KEY (actress_id) REFERENCES actresses (id) ON DELETE CASCADE,
                UNIQUE(av_id, actress_id)
            )
        ''')

        # 检查是否需要添加director字段到现有表
        cursor.execute("PRAGMA table_info(av_info)")
        columns = [column[1] for column in cursor.fetchall()]
        if 'director' not in columns:
            cursor.execute('ALTER TABLE av_info ADD COLUMN director TEXT')

        conn.commit()
        conn.close()
    
    def search_by_code(self, code, overwrite_mode=True, max_pages=None, progress_callback=None):
        """根据番号搜索"""
        search_url = f"{self.base_url}/works?q={code}"
        return self._scrape_search_results(search_url, overwrite_mode, max_pages, progress_callback,
                                         filter_func=lambda av_code: av_code.upper() == code.upper())

    def search_by_prefix(self, prefix, overwrite_mode=True, max_pages=None, progress_callback=None):
        """根据番号前缀搜索"""
        # 自动为前缀添加分隔符以提高搜索精确度
        search_prefix = prefix if prefix.endswith('-') else f"{prefix}-"
        search_url = f"{self.base_url}/works?q={search_prefix}"
        # 改进过滤逻辑：由于搜索时自动添加了分隔符，现在只需要简单的前缀匹配
        def prefix_filter(av_code):
            code_upper = av_code.upper()
            # 使用原始前缀（用户输入的）进行过滤，确保匹配正确的系列
            original_prefix = prefix.upper()

            # 检查番号是否以原始前缀开头
            if code_upper.startswith(original_prefix):
                # 完全匹配
                if len(code_upper) == len(original_prefix):
                    return True

                # 检查前缀后的字符
                next_char = code_upper[len(original_prefix)]
                # 前缀后应该是分隔符或数字，避免CA匹配到CAWD
                return next_char in '-0123456789'
            return False

        return self._scrape_search_results(search_url, overwrite_mode, max_pages, progress_callback,
                                         filter_func=prefix_filter)

    def search_by_number_range(self, prefix, start_num, end_num, overwrite_mode=True, progress_callback=None):
        """根据番号前缀和序号范围搜索"""
        results = []
        stats = {'added': 0, 'updated': 0, 'skipped': 0, 'failed': 0, 'filtered': 0, 'pages_searched': 0}

        total_numbers = end_num - start_num + 1

        for num in range(start_num, end_num + 1):
            # 格式化番号，通常是3位数字，如 CA-001
            code = f"{prefix}-{num:03d}"

            if progress_callback:
                progress_callback('number_progress', {
                    'current': num - start_num + 1,
                    'total': total_numbers,
                    'code': code
                })

            # 搜索单个番号
            single_results, single_stats = self.search_by_code(code, overwrite_mode, max_pages=1,
                                                             progress_callback=progress_callback)

            # 合并结果
            results.extend(single_results)
            for key in ['added', 'updated', 'skipped', 'failed', 'filtered']:
                stats[key] += single_stats.get(key, 0)
            stats['pages_searched'] += single_stats.get('pages_searched', 0)

        return results, stats

    def search_from_file(self, file_path, overwrite_mode=True, progress_callback=None):
        """从文件导入番号前缀列表进行搜索"""
        results = []
        stats = {'added': 0, 'updated': 0, 'skipped': 0, 'failed': 0, 'filtered': 0, 'pages_searched': 0}

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                prefixes = [line.strip() for line in f if line.strip()]

            total_prefixes = len(prefixes)

            for i, prefix in enumerate(prefixes):
                if progress_callback:
                    progress_callback('file_progress', {
                        'current': i + 1,
                        'total': total_prefixes,
                        'prefix': prefix
                    })

                # 搜索每个前缀
                prefix_results, prefix_stats = self.search_by_prefix(prefix, overwrite_mode, max_pages=None,
                                                                   progress_callback=progress_callback)

                # 合并结果
                results.extend(prefix_results)
                for key in ['added', 'updated', 'skipped', 'failed', 'filtered']:
                    stats[key] += prefix_stats.get(key, 0)
                stats['pages_searched'] += prefix_stats.get('pages_searched', 0)

            return results, stats

        except Exception as e:
            print(f"读取文件时出错: {e}")
            stats['failed'] += 1
            return results, stats
    
    def _scrape_search_results(self, url, overwrite_mode=True, max_pages=None, progress_callback=None, filter_func=None):
        """爬取搜索结果"""
        results = []
        stats = {'added': 0, 'updated': 0, 'skipped': 0, 'failed': 0, 'pages_searched': 0, 'filtered': 0}
        page = 1

        # 如果max_pages为None，表示无限制
        while max_pages is None or page <= max_pages:
            try:
                page_url = f"{url}&page={page}"

                # 通知开始搜索新页面
                if progress_callback:
                    progress_callback('page_start', {'page': page, 'url': page_url})

                response = self.session.get(page_url, timeout=15)
                if response.status_code != 200:
                    break

                soup = BeautifulSoup(response.text, 'html.parser')

                # 查找作品链接 - 优先查找带有标题class的链接，然后查找所有作品链接
                work_links = soup.find_all('a', {'class': re.compile(r'.*font-bold.*'), 'href': re.compile(r'/works/[^/]+:[^/]+')})
                if not work_links:
                    # 如果没找到带class的链接，则查找所有作品链接
                    work_links = soup.find_all('a', href=re.compile(r'/works/[^/]+:[^/]+'))

                if not work_links:
                    break

                found_items = False
                for i, link in enumerate(work_links):
                    # 跳过日期链接等
                    href = link.get('href', '')
                    if '/works/date' in href:
                        continue

                    # 检查链接是否在"この作品が収録されている作品"区域内
                    skip_link = False

                    # 向上查找几层父级元素，检查是否包含收录作品的标识
                    current_elem = link
                    for _ in range(5):  # 最多向上查找5层
                        if current_elem:
                            # 检查当前元素及其兄弟元素的文本
                            if current_elem.get_text and 'この作品が収録されている' in current_elem.get_text():
                                skip_link = True
                                break
                            # 检查前面的兄弟元素
                            prev_siblings = current_elem.find_previous_siblings()
                            for sibling in prev_siblings[:3]:  # 检查前3个兄弟元素
                                if sibling.get_text and 'この作品が収録されている' in sibling.get_text():
                                    skip_link = True
                                    break
                            if skip_link:
                                break
                            current_elem = current_elem.parent
                        else:
                            break

                    if skip_link:
                        continue

                    # 通知开始处理单个作品
                    if progress_callback:
                        progress_callback('item_start', {
                            'page': page,
                            'item': i + 1,
                            'total_items': len(work_links),
                            'title': link.text.strip()
                        })

                    av_info = self._get_work_details(href, link.text.strip())
                    if av_info:
                        # 应用过滤器检查番号是否匹配
                        if filter_func and not filter_func(av_info['code']):
                            stats['filtered'] += 1
                            if progress_callback:
                                progress_callback('item_result', {
                                    'result': 'filtered',
                                    'av_info': av_info,
                                    'stats': stats.copy()
                                })
                            continue

                        save_result = self._save_to_db(av_info, overwrite_mode)
                        stats[save_result] += 1

                        # 通知处理结果
                        if progress_callback:
                            progress_callback('item_result', {
                                'result': save_result,
                                'av_info': av_info,
                                'stats': stats.copy()
                            })

                        # 只有成功保存或更新的才加入结果
                        if save_result in ['added', 'updated']:
                            results.append(av_info)
                        found_items = True

                if not found_items:
                    break

                stats['pages_searched'] = page
                page += 1

            except Exception as e:
                print(f"爬取第{page}页时出错: {e}")
                stats['failed'] += 1
                break

        # 确保记录最后搜索的页数
        if stats['pages_searched'] == 0 and page > 1:
            stats['pages_searched'] = page - 1

        return results, stats
    
    def _get_work_details(self, work_href, title):
        """获取单个作品的详细信息"""
        try:
            # 从链接中提取番号
            code_match = re.search(r'/works/([^/]+):([^/]+)', work_href)
            if not code_match:
                return None

            maker_code = code_match.group(1)
            code = code_match.group(2)

            # 访问作品详情页面
            detail_url = f"{self.base_url}{work_href}"
            response = self.session.get(detail_url, timeout=15)
            if response.status_code != 200:
                return None

            soup = BeautifulSoup(response.text, 'html.parser')

            # 从详情页面的h1标签获取标题
            page_title = "Unknown Title"
            h1_elem = soup.find('h1', class_='text-lg')
            if h1_elem and h1_elem.text.strip():
                page_title = h1_elem.text.strip()
            else:
                # 如果没找到h1，尝试其他h1标签
                h1_elem = soup.find('h1')
                if h1_elem and h1_elem.text.strip():
                    page_title = h1_elem.text.strip()
                else:
                    # 最后使用传入的标题作为备选
                    page_title = title.strip() if title else "Unknown Title"

            # 提取封面图片
            cover_image = None
            img_elem = soup.find('img', src=re.compile(r'pics\.dmm\.co\.jp'))
            if img_elem:
                cover_image = img_elem.get('src')

            # 提取发布日期
            release_date = None
            date_elem = soup.find('a', href=re.compile(r'/works/date/\d{4}-\d{2}-\d{2}'))
            if date_elem:
                release_date = date_elem.text.strip()

            # 提取制作商
            maker = maker_code
            maker_elem = soup.find('a', href=re.compile(r'/makers/'))
            if maker_elem:
                maker = maker_elem.text.strip()

            # 提取厂牌
            label = None
            label_elem = soup.find('a', href=re.compile(r'/labels/'))
            if label_elem:
                label = label_elem.text.strip()

            # 提取系列
            series = None
            series_elem = soup.find('a', href=re.compile(r'/series/'))
            if series_elem:
                series = series_elem.text.strip()

            # 提取监督（导演）
            director = None
            # 查找监督链接，通常在页面中以特定格式出现
            director_patterns = [
                r'/works\?q=[^"]*ヒモパン・オブ・ジョイトイ[^"]*',  # 特定监督模式
                r'/works\?q=[^"]*監督[^"]*',  # 包含"监督"的链接
                r'/works\?q=[^"]*director[^"]*'  # 包含"director"的链接
            ]

            for pattern in director_patterns:
                director_links = soup.find_all('a', href=re.compile(pattern))
                for link in director_links:
                    # 从链接文本或URL中提取监督名称
                    link_text = link.text.strip()
                    if link_text and '監督' not in link_text:  # 避免包含"监督"字样的文本
                        director = link_text
                        break
                if director:
                    break

            # 如果没有找到监督链接，尝试从页面文本中提取
            if not director:
                # 查找包含监督信息的文本模式
                page_text = soup.get_text()
                director_match = re.search(r'監督[：:]\s*([^\n\r]+)', page_text)
                if director_match:
                    director = director_match.group(1).strip()

            # 提取演员
            actresses = []
            actress_links = soup.find_all('a', href=re.compile(r'/talents/'))
            seen_urls = set()  # 用URL去重，避免重复演员

            for link in actress_links:
                actress_url = link.get('href', '')
                actress_name = link.text.strip()

                # 移除演员名字后面的数字后缀（如"新井リマ1" -> "新井リマ"）
                actress_name = re.sub(r'\d+$', '', actress_name)

                if actress_url and actress_url not in seen_urls and actress_name:
                    seen_urls.add(actress_url)
                    if actress_name not in actresses:
                        actresses.append(actress_name)

            # 提取DMM链接和CID
            dmm_url = None
            cid = None
            fanza_links = soup.find_all('a', href=re.compile(r'dmm\.co\.jp'))
            for fanza_link in fanza_links:
                dmm_url = fanza_link.get('href')
                # 从URL中提取实际的DMM链接
                if 'lurl=' in dmm_url:
                    import urllib.parse
                    try:
                        parsed = urllib.parse.parse_qs(urllib.parse.urlparse(dmm_url).query)
                        if 'lurl' in parsed:
                            dmm_url = urllib.parse.unquote(parsed['lurl'][0])
                    except:
                        pass

                cid_match = re.search(r'cid=([^&/]+)', dmm_url)
                if cid_match:
                    cid = cid_match.group(1)
                    break

            # 如果没有找到CID，记录为空
            if not cid:
                print(f"⚠️ 番号 {code} 没有找到CID，将记录为空")

            return {
                'code': code,
                'title': page_title,  # 使用从详情页面提取的完整标题
                'release_date': release_date,
                'maker': maker,
                'label': label,
                'series': series,
                'director': director,  # 添加监督信息
                'actresses': actresses,
                'dmm_url': dmm_url,
                'cid': cid,
                'cover_image': cover_image
            }

        except Exception as e:
            print(f"获取作品详情时出错: {e}")
            return None


    
    def _save_to_db(self, av_info, overwrite_mode=True):
        """保存到数据库
        Args:
            av_info: 作品信息字典
            overwrite_mode: True=覆盖已有记录, False=跳过已有记录
        Returns:
            str: 'added', 'updated', 'skipped', 'failed'
        """
        code = av_info.get('code')
        maker = av_info.get('maker') or ''  # 制作商可以为空

        if not code:
            print(f"⚠️ 没有番号信息，跳过保存")
            return 'failed'

        conn = sqlite3.connect('avbase.db')
        cursor = conn.cursor()

        try:
            # 检查记录是否已存在（使用番号+制作商联合唯一键）
            cursor.execute('SELECT id FROM av_info WHERE code = ? AND maker = ?', (code, maker))
            existing = cursor.fetchone()

            if existing and not overwrite_mode:
                return 'skipped'

            av_id = None
            if existing:
                # 更新现有记录
                av_id = existing[0]
                cursor.execute('''
                    UPDATE av_info SET
                        cid = ?, title = ?, release_date = ?, label = ?,
                        series = ?, director = ?, dmm_url = ?, cover_image = ?
                    WHERE code = ? AND maker = ?
                ''', (
                    av_info.get('cid'),  # 允许为None/空
                    av_info.get('title', ''),
                    av_info.get('release_date', ''),
                    av_info.get('label', ''),
                    av_info.get('series', ''),
                    av_info.get('director', ''),  # 添加监督字段
                    av_info.get('dmm_url', ''),
                    av_info.get('cover_image', ''),
                    av_info.get('code', ''),
                    maker
                ))

                # 删除现有的演员关联
                cursor.execute('DELETE FROM av_actresses WHERE av_id = ?', (av_id,))
                result = 'updated'
            else:
                # 插入新记录
                cursor.execute('''
                    INSERT INTO av_info
                    (code, maker, cid, title, release_date, label, series, director, dmm_url, cover_image)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    av_info.get('code', ''),
                    maker,  # 使用处理后的maker（空字符串而不是None）
                    av_info.get('cid'),  # 允许为None/空
                    av_info.get('title', ''),
                    av_info.get('release_date', ''),
                    av_info.get('label', ''),
                    av_info.get('series', ''),
                    av_info.get('director', ''),  # 添加监督字段
                    av_info.get('dmm_url', ''),
                    av_info.get('cover_image', '')
                ))
                av_id = cursor.lastrowid
                result = 'added'

            # 处理演员信息
            actresses = av_info.get('actresses', [])
            if actresses:
                self._save_actresses(cursor, av_id, actresses)

            conn.commit()
            return result

        except Exception as e:
            print(f"保存到数据库时出错: {e}")
            print(f"数据内容: {av_info}")
            import traceback
            traceback.print_exc()
            return 'failed'
        finally:
            conn.close()

    def _save_actresses(self, cursor, av_id, actresses):
        """保存演员信息到演员表和关联表"""
        for actress_name in actresses:
            if not actress_name.strip():
                continue

            # 检查演员是否已存在
            cursor.execute('SELECT id FROM actresses WHERE name = ?', (actress_name,))
            actress_record = cursor.fetchone()

            if actress_record:
                actress_id = actress_record[0]
            else:
                # 插入新演员
                cursor.execute('INSERT INTO actresses (name) VALUES (?)', (actress_name,))
                actress_id = cursor.lastrowid

            # 创建影片-演员关联（如果不存在）
            cursor.execute('''
                INSERT OR IGNORE INTO av_actresses (av_id, actress_id)
                VALUES (?, ?)
            ''', (av_id, actress_id))

    def _get_actresses_for_av(self, av_id):
        """获取指定影片的演员列表"""
        conn = sqlite3.connect('avbase.db')
        cursor = conn.cursor()
        try:
            cursor.execute('''
                SELECT a.name FROM actresses a
                JOIN av_actresses aa ON a.id = aa.actress_id
                WHERE aa.av_id = ?
                ORDER BY a.name
            ''', (av_id,))
            return [row[0] for row in cursor.fetchall()]
        finally:
            conn.close()

class AVBaseGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("AVBASE 信息抓取工具 v2.0")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')

        # 设置窗口图标和样式
        self.root.resizable(True, True)
        self.root.minsize(800, 600)

        # 配置样式
        self.style = ttk.Style()
        self.style.theme_use('clam')

        # 现代化配色方案
        self.colors = {
            'primary': '#3498db',      # 蓝色主色调
            'secondary': '#2c3e50',    # 深蓝灰色
            'success': '#27ae60',      # 绿色
            'warning': '#f39c12',      # 橙色
            'danger': '#e74c3c',       # 红色
            'light': '#ecf0f1',        # 浅灰色
            'dark': '#34495e',         # 深灰色
            'text': '#2c3e50',         # 文本色
            'muted': '#7f8c8d',        # 次要文本色
            'bg': '#f8f9fa'            # 背景色
        }

        # 自定义样式
        self.style.configure('Title.TLabel',
                           font=('Microsoft YaHei', 18, 'bold'),
                           foreground=self.colors['primary'])

        self.style.configure('Heading.TLabel',
                           font=('Microsoft YaHei', 12, 'bold'),
                           foreground=self.colors['secondary'])

        self.style.configure('Custom.TButton',
                           font=('Microsoft YaHei', 11, 'bold'),
                           foreground='white',
                           background=self.colors['primary'],
                           borderwidth=0,
                           focuscolor='none',
                           padding=(20, 10))

        # 重试按钮样式
        self.style.configure('Retry.TButton',
                           font=('Microsoft YaHei', 10, 'bold'),
                           foreground='white',
                           background=self.colors['warning'],
                           borderwidth=0,
                           focuscolor='none',
                           padding=(15, 8))

        self.style.configure('Search.TEntry',
                           font=('Microsoft YaHei', 11),
                           fieldbackground='white',
                           borderwidth=2)

        self.style.configure('Custom.TRadiobutton',
                           font=('Microsoft YaHei', 10),
                           foreground=self.colors['text'],
                           background='white',
                           focuscolor='none')

        # 进度条样式
        self.style.configure('Custom.Horizontal.TProgressbar',
                           background=self.colors['primary'],
                           troughcolor=self.colors['light'],
                           borderwidth=1,
                           lightcolor=self.colors['primary'],
                           darkcolor=self.colors['primary'])

        self.scraper = AVBaseScraper()

        # 界面变量
        self.query_var = tk.StringVar()
        self.search_type_var = tk.StringVar(value="exact")
        self.overwrite_var = tk.BooleanVar(value=True)
        self.max_pages_var = tk.StringVar(value="20")  # 默认最大页数
        self.unlimited_pages_var = tk.BooleanVar(value=False)  # 是否无限制

        # 序号范围搜索变量
        self.range_prefix_var = tk.StringVar()
        self.range_start_var = tk.StringVar(value="1")
        self.range_end_var = tk.StringVar(value="100")

        # 文件导入变量
        self.file_path_var = tk.StringVar()

        self.last_search_failed = False

        self.setup_ui()
    
    def setup_ui(self):
        # 设置根窗口背景
        self.root.configure(bg=self.colors['bg'])

        # 创建主容器
        main_container = tk.Frame(self.root, bg=self.colors['bg'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 标题区域
        title_frame = tk.Frame(main_container, bg=self.colors['bg'])
        title_frame.pack(fill=tk.X, pady=(0, 30))

        title_label = tk.Label(title_frame, text="🎬 AVBASE 信息抓取工具",
                              font=('Microsoft YaHei', 18, 'bold'),
                              foreground=self.colors['primary'],
                              bg=self.colors['bg'])
        title_label.pack()

        subtitle_label = tk.Label(title_frame, text="专业的AV信息搜索与管理工具",
                                 font=('Microsoft YaHei', 10),
                                 foreground='#7f8c8d',
                                 bg=self.colors['bg'])
        subtitle_label.pack(pady=(5, 0))

        # 搜索区域
        search_frame = tk.LabelFrame(main_container, text=" 搜索设置 ", bg='white',
                                   font=('Microsoft YaHei', 11, 'bold'), fg=self.colors['secondary'],
                                   relief=tk.RAISED, bd=2)
        search_frame.pack(fill=tk.X, pady=(0, 20), padx=10, ipady=15)

        # 搜索类型选择
        type_frame = tk.Frame(search_frame, bg='white')
        type_frame.pack(fill=tk.X, padx=20, pady=(10, 15))

        tk.Label(type_frame, text="搜索类型:",
                 font=('Microsoft YaHei', 12, 'bold'),
                 foreground=self.colors['secondary'],
                 bg='white').pack(side=tk.LEFT)

        self.search_type = tk.StringVar(value="code")
        radio_frame = tk.Frame(type_frame, bg='white')
        radio_frame.pack(side=tk.LEFT, padx=(20, 0))

        ttk.Radiobutton(radio_frame, text="🎯 精确番号搜索", variable=self.search_type,
                       value="code", style='Custom.TRadiobutton',
                       command=self._on_search_type_change).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(radio_frame, text="📚 番号前缀搜索", variable=self.search_type,
                       value="prefix", style='Custom.TRadiobutton',
                       command=self._on_search_type_change).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(radio_frame, text="🔢 序号范围搜索", variable=self.search_type,
                       value="range", style='Custom.TRadiobutton',
                       command=self._on_search_type_change).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(radio_frame, text="📁 文件导入搜索", variable=self.search_type,
                       value="file", style='Custom.TRadiobutton',
                       command=self._on_search_type_change).pack(side=tk.LEFT)

        # 覆盖选项
        option_frame = tk.Frame(search_frame, bg='white')
        option_frame.pack(fill=tk.X, padx=20, pady=(0, 15))

        tk.Label(option_frame, text="数据处理:",
                 font=('Microsoft YaHei', 12, 'bold'),
                 foreground=self.colors['secondary'],
                 bg='white').pack(side=tk.LEFT)

        overwrite_frame = tk.Frame(option_frame, bg='white')
        overwrite_frame.pack(side=tk.LEFT, padx=(20, 0))

        ttk.Radiobutton(overwrite_frame, text="🔄 覆盖已有记录", variable=self.overwrite_var,
                       value=True, style='Custom.TRadiobutton').pack(side=tk.LEFT, padx=(0, 30))
        ttk.Radiobutton(overwrite_frame, text="⏭️ 跳过已有记录", variable=self.overwrite_var,
                       value=False, style='Custom.TRadiobutton').pack(side=tk.LEFT)

        # 页数限制选项
        pages_frame = tk.Frame(search_frame, bg='white')
        pages_frame.pack(fill=tk.X, padx=20, pady=(0, 15))

        tk.Label(pages_frame, text="搜索范围:",
                 font=('Microsoft YaHei', 12, 'bold'),
                 foreground=self.colors['secondary'],
                 bg='white').pack(side=tk.LEFT)

        pages_control_frame = tk.Frame(pages_frame, bg='white')
        pages_control_frame.pack(side=tk.LEFT, padx=(20, 0))

        # 无限制选项
        ttk.Checkbutton(pages_control_frame, text="🔄 搜索所有页面", variable=self.unlimited_pages_var,
                       style='Custom.TRadiobutton', command=self._toggle_pages_limit).pack(side=tk.LEFT, padx=(0, 20))

        # 页数限制输入
        pages_limit_frame = tk.Frame(pages_control_frame, bg='white')
        pages_limit_frame.pack(side=tk.LEFT)

        tk.Label(pages_limit_frame, text="最大页数:",
                 font=('Microsoft YaHei', 10),
                 foreground=self.colors['text'],
                 bg='white').pack(side=tk.LEFT)

        self.max_pages_entry = ttk.Entry(pages_limit_frame, textvariable=self.max_pages_var,
                                       width=5, font=('Microsoft YaHei', 10))
        self.max_pages_entry.pack(side=tk.LEFT, padx=(5, 0))

        # 动态输入区域容器
        self.input_container = tk.Frame(search_frame, bg='white')
        self.input_container.pack(fill=tk.X, padx=20, pady=(0, 15))

        # 创建不同类型的输入界面
        self._create_input_interfaces()

        # 初始显示精确搜索界面
        self._on_search_type_change()
        self.query_entry.bind('<Return>', self._on_enter_key)

        # 按钮容器
        button_frame = tk.Frame(search_frame, bg='white')
        button_frame.pack(fill=tk.X, padx=20, pady=(10, 0))

        # 搜索按钮
        self.search_btn = ttk.Button(button_frame, text="🔍 开始搜索", command=self.search,
                                   style='Custom.TButton')
        self.search_btn.pack(side=tk.RIGHT, padx=(15, 0))

        # 重试按钮（初始隐藏）
        self.retry_btn = ttk.Button(button_frame, text="🔄 重试搜索", command=self.retry_search,
                                  style='Retry.TButton')
        self.retry_btn.pack(side=tk.RIGHT, padx=(8, 0))
        self.retry_btn.pack_forget()  # 初始隐藏

        # 进度条区域
        progress_frame = tk.Frame(search_frame, bg='white')
        progress_frame.pack(fill=tk.X, padx=20, pady=(5, 15))

        # 美化的进度条
        self.progress = ttk.Progressbar(progress_frame, mode='indeterminate',
                                      length=500, style='Custom.Horizontal.TProgressbar')
        self.progress.pack(pady=5)

        # 进度状态标签
        self.progress_label = tk.Label(progress_frame, text="",
                                     font=('Microsoft YaHei', 9),
                                     foreground=self.colors['muted'],
                                     bg='white')
        self.progress_label.pack(pady=(2, 0))

        # 结果区域
        result_frame = tk.LabelFrame(main_container, text=" 搜索结果 ", bg='white',
                                   font=('Microsoft YaHei', 11, 'bold'), fg='#2c3e50',
                                   relief=tk.RAISED, bd=2)
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10)

        # 结果文本框
        text_frame = tk.Frame(result_frame, bg='white')
        text_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        self.result_text = scrolledtext.ScrolledText(text_frame, wrap=tk.WORD,
                                                   font=('Microsoft YaHei', 10),
                                                   bg='white', fg='#2c3e50',
                                                   relief=tk.FLAT, bd=1,
                                                   selectbackground='#3498db',
                                                   selectforeground='white')
        self.result_text.pack(fill=tk.BOTH, expand=True)

        # 添加占位符文本
        self.result_text.insert(tk.END, "💡 使用提示:\n")
        self.result_text.insert(tk.END, "• 精确搜索: 输入完整番号，如 MIAB-385\n")
        self.result_text.insert(tk.END, "• 前缀搜索: 输入番号前缀，如 MIAB\n")
        self.result_text.insert(tk.END, "• 支持批量搜索和数据库存储\n\n")
        self.result_text.insert(tk.END, "准备就绪，请输入搜索内容...")

        # 设置文本样式
        self.result_text.tag_configure("title", font=('Microsoft YaHei', 12, 'bold'), foreground=self.colors['primary'])
        self.result_text.tag_configure("label", font=('Microsoft YaHei', 10, 'bold'), foreground=self.colors['success'])
        self.result_text.tag_configure("content", font=('Microsoft YaHei', 10), foreground=self.colors['text'])
        self.result_text.tag_configure("link", font=('Microsoft YaHei', 10), foreground=self.colors['danger'], underline=True)
        self.result_text.tag_configure("success", font=('Microsoft YaHei', 9), foreground='#27ae60')
        self.result_text.tag_configure("warning", font=('Microsoft YaHei', 9), foreground='#f39c12')
        self.result_text.tag_configure("error", font=('Microsoft YaHei', 9), foreground='#e74c3c')

        # 初始化页数限制控件状态
        self._toggle_pages_limit()

    def _create_input_interfaces(self):
        """创建不同类型的输入界面"""

        # 1. 精确搜索和前缀搜索界面（共用）
        self.simple_input_frame = tk.Frame(self.input_container, bg='white')

        tk.Label(self.simple_input_frame, text="输入内容:",
                 font=('Microsoft YaHei', 12, 'bold'),
                 foreground=self.colors['secondary'],
                 bg='white').pack(side=tk.LEFT)

        entry_frame = tk.Frame(self.simple_input_frame, bg='white')
        entry_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(20, 0))

        self.query_entry = ttk.Entry(entry_frame, textvariable=self.query_var,
                                   style='Search.TEntry', font=('Microsoft YaHei', 12))
        self.query_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, ipady=8)
        self.query_entry.bind('<Return>', self._on_enter_key)

        # 2. 序号范围搜索界面
        self.range_input_frame = tk.Frame(self.input_container, bg='white')

        tk.Label(self.range_input_frame, text="序号范围搜索:",
                 font=('Microsoft YaHei', 12, 'bold'),
                 foreground=self.colors['secondary'],
                 bg='white').pack(side=tk.LEFT)

        range_controls = tk.Frame(self.range_input_frame, bg='white')
        range_controls.pack(side=tk.LEFT, padx=(20, 0))

        # 前缀输入
        tk.Label(range_controls, text="前缀:", font=('Microsoft YaHei', 10),
                 foreground=self.colors['text'], bg='white').pack(side=tk.LEFT)
        ttk.Entry(range_controls, textvariable=self.range_prefix_var, width=8,
                 font=('Microsoft YaHei', 10)).pack(side=tk.LEFT, padx=(5, 15))

        # 开始序号
        tk.Label(range_controls, text="开始:", font=('Microsoft YaHei', 10),
                 foreground=self.colors['text'], bg='white').pack(side=tk.LEFT)
        ttk.Entry(range_controls, textvariable=self.range_start_var, width=6,
                 font=('Microsoft YaHei', 10)).pack(side=tk.LEFT, padx=(5, 15))

        # 结束序号
        tk.Label(range_controls, text="结束:", font=('Microsoft YaHei', 10),
                 foreground=self.colors['text'], bg='white').pack(side=tk.LEFT)
        ttk.Entry(range_controls, textvariable=self.range_end_var, width=6,
                 font=('Microsoft YaHei', 10)).pack(side=tk.LEFT, padx=(5, 0))

        # 3. 文件导入搜索界面
        self.file_input_frame = tk.Frame(self.input_container, bg='white')

        tk.Label(self.file_input_frame, text="文件导入:",
                 font=('Microsoft YaHei', 12, 'bold'),
                 foreground=self.colors['secondary'],
                 bg='white').pack(side=tk.LEFT)

        file_controls = tk.Frame(self.file_input_frame, bg='white')
        file_controls.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(20, 0))

        ttk.Entry(file_controls, textvariable=self.file_path_var,
                 style='Search.TEntry', font=('Microsoft YaHei', 11)).pack(side=tk.LEFT, fill=tk.X, expand=True, ipady=6)

        ttk.Button(file_controls, text="浏览", command=self._browse_file,
                  style='Custom.TButton').pack(side=tk.LEFT, padx=(10, 0))

    def _on_search_type_change(self):
        """搜索类型改变时的处理"""
        # 隐藏所有输入界面
        for frame in [self.simple_input_frame, self.range_input_frame, self.file_input_frame]:
            frame.pack_forget()

        # 根据选择显示对应界面
        search_type = self.search_type.get()
        if search_type in ["code", "prefix"]:
            self.simple_input_frame.pack(fill=tk.X)
            # 设置提示文本
            if search_type == "code":
                self.query_entry.delete(0, tk.END)
                self.query_entry.insert(0, "例如: MIAB-025")
                self.query_entry.select_range(0, tk.END)
            else:
                self.query_entry.delete(0, tk.END)
                self.query_entry.insert(0, "例如: MIAB")
                self.query_entry.select_range(0, tk.END)
        elif search_type == "range":
            self.range_input_frame.pack(fill=tk.X)
        elif search_type == "file":
            self.file_input_frame.pack(fill=tk.X)

    def _browse_file(self):
        """浏览文件"""
        from tkinter import filedialog
        filename = filedialog.askopenfilename(
            title="选择番号前缀文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if filename:
            self.file_path_var.set(filename)

    def _on_enter_key(self, event=None):
        """处理回车键事件"""
        self.search()

    def _toggle_pages_limit(self):
        """切换页数限制状态"""
        if self.unlimited_pages_var.get():
            self.max_pages_entry.config(state='disabled')
        else:
            self.max_pages_entry.config(state='normal')

    def _progress_callback(self, event_type, data):
        """进度回调函数"""
        self.root.after(0, self._update_progress, event_type, data)

    def _update_progress(self, event_type, data):
        """更新进度显示"""
        if event_type == 'page_start':
            page = data['page']
            self.progress_label.config(text=f"🔍 正在搜索第 {page} 页...")
            self.result_text.insert(tk.END, f"\n📄 开始搜索第 {page} 页...\n", "label")
            self.result_text.see(tk.END)

        elif event_type == 'number_progress':
            current = data['current']
            total = data['total']
            code = data['code']
            self.progress_label.config(text=f"🔢 序号搜索 {current}/{total}: {code}")
            if current % 10 == 1:  # 每10个显示一次
                self.result_text.insert(tk.END, f"🔢 正在搜索: {code} ({current}/{total})\n", "label")
                self.result_text.see(tk.END)

        elif event_type == 'file_progress':
            current = data['current']
            total = data['total']
            prefix = data['prefix']
            self.progress_label.config(text=f"📁 文件导入 {current}/{total}: {prefix}")
            self.result_text.insert(tk.END, f"📁 正在搜索前缀: {prefix} ({current}/{total})\n", "label")
            self.result_text.see(tk.END)

        elif event_type == 'item_start':
            page = data['page']
            item = data['item']
            total = data['total_items']
            title = data['title']
            self.progress_label.config(text=f"📄 第{page}页 - 处理 {item}/{total}: {title[:30]}...")

        elif event_type == 'item_result':
            result = data['result']
            av_info = data['av_info']
            stats = data['stats']

            # 根据结果显示不同的图标和颜色
            if result == 'added':
                icon = "✅"
                tag = "success"
            elif result == 'updated':
                icon = "🔄"
                tag = "content"
            elif result == 'skipped':
                icon = "⏭️"
                tag = "content"
            elif result == 'filtered':
                icon = "🚫"
                tag = "warning"
            else:  # failed
                icon = "❌"
                tag = "error"

            # 显示处理结果
            code = av_info.get('code', 'Unknown')
            title = av_info.get('title', 'Unknown')[:40]
            self.result_text.insert(tk.END, f"{icon} {result.upper()}: {code} - {title}\n", tag)

            # 每10条更新一次统计
            total_processed = stats['added'] + stats['updated'] + stats['skipped']
            if total_processed % 10 == 0:
                filtered_info = f" 过滤{stats.get('filtered', 0)}" if stats.get('filtered', 0) > 0 else ""
                self.result_text.insert(tk.END, f"📊 当前统计: 新增{stats['added']} 更新{stats['updated']} 跳过{stats['skipped']} 失败{stats['failed']}{filtered_info}\n", "label")

            self.result_text.see(tk.END)
    
    def search(self):
        search_type = self.search_type.get()

        # 根据搜索类型验证输入
        if search_type in ["code", "prefix"]:
            query = self.query_var.get().strip()
            if not query:
                messagebox.showwarning("警告", "请输入搜索内容")
                return
        elif search_type == "range":
            prefix = self.range_prefix_var.get().strip()
            start_str = self.range_start_var.get().strip()
            end_str = self.range_end_var.get().strip()

            if not prefix or not start_str or not end_str:
                messagebox.showwarning("警告", "请输入完整的前缀和序号范围")
                return

            try:
                start_num = int(start_str)
                end_num = int(end_str)
                if start_num > end_num:
                    messagebox.showwarning("警告", "开始序号不能大于结束序号")
                    return
                if end_num - start_num > 1000:
                    if not messagebox.askyesno("确认", f"序号范围很大({end_num - start_num + 1}个)，可能需要很长时间，是否继续？"):
                        return
            except ValueError:
                messagebox.showwarning("警告", "序号必须是数字")
                return

        elif search_type == "file":
            file_path = self.file_path_var.get().strip()
            if not file_path:
                messagebox.showwarning("警告", "请选择文件")
                return
            if not os.path.exists(file_path):
                messagebox.showwarning("警告", "文件不存在")
                return

        # 隐藏重试按钮
        self.retry_btn.pack_forget()
        self.last_search_failed = False

        # 在新线程中执行搜索
        threading.Thread(target=self._search_thread, daemon=True).start()

    def retry_search(self):
        """重试上次搜索"""
        self.search()
    
    def _search_thread(self):
        # 更新UI状态
        self.root.after(0, self._update_search_start)

        try:
            search_type = self.search_type.get()
            overwrite_mode = self.overwrite_var.get()

            # 获取页数限制设置
            max_pages = None
            if not self.unlimited_pages_var.get():
                try:
                    max_pages = int(self.max_pages_var.get())
                    if max_pages <= 0:
                        max_pages = 20  # 默认值
                except ValueError:
                    max_pages = 20  # 默认值

            # 根据搜索类型执行不同的搜索
            if search_type == "code":
                query = self.query_var.get().strip()
                results, stats = self.scraper.search_by_code(query, overwrite_mode, max_pages, self._progress_callback)
            elif search_type == "prefix":
                query = self.query_var.get().strip()
                results, stats = self.scraper.search_by_prefix(query, overwrite_mode, max_pages, self._progress_callback)
            elif search_type == "range":
                prefix = self.range_prefix_var.get().strip()
                start_num = int(self.range_start_var.get().strip())
                end_num = int(self.range_end_var.get().strip())
                results, stats = self.scraper.search_by_number_range(prefix, start_num, end_num, overwrite_mode, self._progress_callback)
            elif search_type == "file":
                file_path = self.file_path_var.get().strip()
                results, stats = self.scraper.search_from_file(file_path, overwrite_mode, self._progress_callback)

            # 更新结果显示
            self.root.after(0, self._update_results, results, stats)

        except Exception as e:
            self.root.after(0, self._update_error, str(e))
        finally:
            self.root.after(0, self._update_search_end)
    
    def _update_search_start(self):
        self.search_btn.config(state='disabled', text='🔄 搜索中...')
        self.progress.start()
        self.progress_label.config(text="🔍 正在搜索AVBASE数据库...")
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, "🔍 开始搜索AVBASE数据库\n", "title")
        self.result_text.insert(tk.END, "⏳ 正在连接服务器...\n", "content")

    def _update_search_end(self):
        self.search_btn.config(state='normal', text='🔍 开始搜索')
        self.progress.stop()
        self.progress_label.config(text="")

        # 如果搜索失败，显示重试按钮
        if self.last_search_failed:
            self.retry_btn.pack(side=tk.RIGHT, padx=(8, 0))
    
    def _update_results(self, results, stats):
        self.result_text.delete(1.0, tk.END)

        # 显示统计信息
        total_processed = stats['added'] + stats['updated'] + stats['skipped']
        self.result_text.insert(tk.END, "📊 搜索统计\n", "title")
        self.result_text.insert(tk.END, f"📄 搜索页数: {stats.get('pages_searched', 0)} 页\n", "content")
        self.result_text.insert(tk.END, f"🆕 新增: {stats['added']} 条\n", "content")
        self.result_text.insert(tk.END, f"🔄 更新: {stats['updated']} 条\n", "content")
        self.result_text.insert(tk.END, f"⏭️ 跳过: {stats['skipped']} 条\n", "content")
        self.result_text.insert(tk.END, f"❌ 失败: {stats['failed']} 条\n", "content")
        if stats.get('filtered', 0) > 0:
            self.result_text.insert(tk.END, f"🚫 过滤: {stats['filtered']} 条\n", "warning")
        self.result_text.insert(tk.END, f"📈 总计: {total_processed} 条\n\n", "label")

        if not results:
            if total_processed == 0:
                self.last_search_failed = True
                self.result_text.insert(tk.END, "❌ 未找到相关结果\n\n", "content")
                self.result_text.insert(tk.END, "💡 建议:\n", "label")
                self.result_text.insert(tk.END, "• 检查番号是否正确\n", "content")
                self.result_text.insert(tk.END, "• 尝试使用前缀搜索\n", "content")
                self.result_text.insert(tk.END, "• 确认网络连接正常\n", "content")
                self.result_text.insert(tk.END, "• 点击重试按钮再次尝试\n", "content")
            else:
                self.result_text.insert(tk.END, "ℹ️ 所有记录都已存在，已跳过\n", "content")
            return

        # 显示搜索结果
        self.result_text.insert(tk.END, f"🎉 搜索结果 ({len(results)} 条)\n\n", "title")

        for i, item in enumerate(results, 1):
            # 分隔线
            self.result_text.insert(tk.END, "=" * 80 + "\n", "content")

            # 番号标题 (CID在番号后面)
            cid = item.get('cid')
            if cid:
                cid_display = f" ({cid})"
            else:
                cid_display = " (无CID)"
            self.result_text.insert(tk.END, f"📀 [{i}] {item['code']}{cid_display}\n", "title")

            # 作品标题
            if item['title']:
                self.result_text.insert(tk.END, f"🎬 标题: ", "label")
                self.result_text.insert(tk.END, f"{item['title']}\n", "content")

            # 基本信息
            if item['release_date']:
                self.result_text.insert(tk.END, f"📅 发布日期: ", "label")
                self.result_text.insert(tk.END, f"{item['release_date']}\n", "content")

            maker = item.get('maker')
            if maker:
                self.result_text.insert(tk.END, f"🏢 制作商: ", "label")
                self.result_text.insert(tk.END, f"{maker}\n", "content")
            else:
                self.result_text.insert(tk.END, f"🏢 制作商: ", "label")
                self.result_text.insert(tk.END, f"未知\n", "content")

            label = item.get('label', '').strip()
            if label:
                self.result_text.insert(tk.END, f"🔖 厂牌: ", "label")
                self.result_text.insert(tk.END, f"{label}\n", "content")

            if item['series']:
                self.result_text.insert(tk.END, f"📚 系列: ", "label")
                self.result_text.insert(tk.END, f"{item['series']}\n", "content")

            # 监督信息
            director = item.get('director', '').strip()
            if director:
                self.result_text.insert(tk.END, f"🎬 监督: ", "label")
                self.result_text.insert(tk.END, f"{director}\n", "content")

            # 演员信息
            if item['actresses']:
                self.result_text.insert(tk.END, f"🎭 演员: ", "label")
                self.result_text.insert(tk.END, f"{', '.join(item['actresses'])}\n", "content")

            # 技术信息
            if item['cid']:
                self.result_text.insert(tk.END, f"🔗 CID: ", "label")
                self.result_text.insert(tk.END, f"{item['cid']}\n", "content")

            # 封面图片
            cover_image = item.get('cover_image', '').strip()
            if cover_image:
                self.result_text.insert(tk.END, f"🖼 封面图片: ", "label")
                self.result_text.insert(tk.END, f"{cover_image}\n", "link")

            # FANZA链接
            if item['dmm_url']:
                self.result_text.insert(tk.END, f"🌐 FANZA链接: ", "label")
                self.result_text.insert(tk.END, f"{item['dmm_url']}\n", "link")

            self.result_text.insert(tk.END, "\n", "content")

        # 底部统计
        self.result_text.insert(tk.END, "=" * 80 + "\n", "content")
        self.result_text.insert(tk.END, f"✅ 搜索完成，共处理 {len(results)} 条记录，已保存到数据库\n", "title")
    
    def _update_error(self, error_msg):
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, "❌ 搜索失败\n\n", "title")
        self.result_text.insert(tk.END, f"错误信息: {error_msg}\n\n", "content")
        self.result_text.insert(tk.END, "🔧 解决方案:\n", "label")
        self.result_text.insert(tk.END, "• 检查网络连接\n", "content")
        self.result_text.insert(tk.END, "• 确认AVBASE网站可访问\n", "content")
        self.result_text.insert(tk.END, "• 稍后重试\n", "content")

if __name__ == "__main__":
    root = tk.Tk()
    app = AVBaseGUI(root)
    root.mainloop()
